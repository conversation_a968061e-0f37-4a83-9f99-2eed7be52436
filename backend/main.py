import os
import asyncio
import json
import websockets
from fastapi import FastAP<PERSON>, WebSocket, Query
from fastapi.responses import HTMLResponse
from dotenv import load_dotenv
import openai
from supabase import create_client, Client
import uuid
from datetime import datetime
import tempfile
from utils.audio_utils import save_pcm_to_wav, upload_wav_to_supabase, delete_temp_file
from prompts.summarize import PROMPT_TEXT
from utils.statement_extractor import extract_statement_info

load_dotenv()
app = FastAPI()

SPEECHMATICS_API_KEY = os.environ["SPEECHMATICS_API_KEY"]
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
SUPABASE_URL = os.environ["SUPABASE_URL"]
SUPABASE_KEY = os.environ["SUPABASE_KEY"]
SM_WS_URL = os.environ["SM_WS_URL"]
LANG = "cmn_en"
SAMPLE_RATE = 16000

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

async def safe_websocket_send(websocket, message):
    """
    Safely send a message via WebSocket and handle errors.

    Args:
        websocket (WebSocket): FastAPI WebSocket object.
        message (str): The message to send.

    Returns:
        bool: True if send succeeds, False otherwise.
    """
    try:
        await websocket.send_text(message)
        return True
    except Exception as e:
        print(f"🔍 DEBUG: Error sending WebSocket message: {e}")
        return False


async def generate_summary(segments):
    """
    Generate a summary for the given transcript segments, using OpenAI if available.

    Args:
        segments (list): List of transcript segments.

    Returns:
        str: Generated summary.
    """
    if not segments:
        return "No content to summarize."
    full_transcript = ""
    for segment in segments:
        speaker = segment.get("speaker", "Unknown")
        text = segment.get("text", "")
        full_transcript += f"{speaker}: {text}\n"
    if not OPENAI_API_KEY:
        speakers = set(segment.get("speaker", "Unknown") for segment in segments)
        word_count = sum(len(segment.get("text", "").split()) for segment in segments)
        duration = (
            max(segment.get("end_time", 0) for segment in segments) if segments else 0
        )
        return f"""**Conversation Summary:**
• **Participants:** {', '.join(sorted(speakers))}
• **Duration:** {duration:.1f} seconds
• **Word Count:** {word_count} words
• **Key Points:** This conversation involved {len(speakers)} speaker(s) discussing various topics over {duration:.1f} seconds.

**Transcript Preview:**
{full_transcript[:300]}{'...' if len(full_transcript) > 300 else ''}"""
    try:
        client = openai.OpenAI(api_key=OPENAI_API_KEY)
        response = await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that creates concise summaries of conversations. Focus on key topics, decisions, and action items.",
                    },
                    {
                        "role": "user",
                        "content": PROMPT_TEXT.format(transcript=full_transcript),
                    },
                ],
                max_tokens=300,
                temperature=0.3,
            ),
        )
        print(response.choices[0].message.content)
        return response.choices[0].message.content
    except Exception as e:
        print(f"AI summarization failed: {e}")
        speakers = set(segment.get("speaker", "Unknown") for segment in segments)
        word_count = sum(len(segment.get("text", "").split()) for segment in segments)
        duration = (
            max(segment.get("end_time", 0) for segment in segments) if segments else 0
        )
        return f"""**Conversation Summary:**
• **Participants:** {', '.join(sorted(speakers))}
• **Duration:** {duration:.1f} seconds  
• **Word Count:** {word_count} words

**Content:** The conversation covered various topics between the participants. For a detailed summary, please configure an AI API key.
        """


def transform_speechmatics_to_desired_format(sm_response):
    """
    Transform a Speechmatics response to the desired transcript segment format.

    Args:
        sm_response (dict): The response from Speechmatics.

    Returns:
        list: List of transformed transcript segments.
    """
    if sm_response.get("message") != "AddTranscript":
        return None
    results = sm_response.get("results", [])
    transformed_segments = []
    current_segment = None

    for result in results:
        if result.get("type") == "word":
            alternatives = result.get("alternatives", [])
            if alternatives:
                word_data = alternatives[0]
                speaker = word_data.get("speaker", "S1")
                content = word_data.get("content", "")
                start_time = result.get("start_time", 0)
                end_time = result.get("end_time", 0)

                # Aggregate words into a segment
                if current_segment and current_segment["speaker"] == speaker:
                    current_segment["text"] += " " + content
                    current_segment["end_time"] = end_time
                else:
                    if current_segment:
                        transformed_segments.append(current_segment)
                    current_segment = {
                        "text": content,
                        "speaker": speaker,
                        "start_time": start_time,
                        "end_time": end_time,
                    }

        elif result.get("type") == "punctuation":
            alternatives = result.get("alternatives", [])
            if alternatives:
                punctuation_data = alternatives[0]
                speaker = punctuation_data.get("speaker", "S1")
                content = punctuation_data.get("content", "")
                start_time = result.get("start_time", 0)
                end_time = result.get("end_time", 0)
                print(
                    f"🔤 [DEBUG] Punctuation: '{content}' Speaker: {speaker} Start Time: {start_time}, End Time: {end_time}"
                )
                if current_segment and current_segment["speaker"] == speaker:
                    current_segment["text"] += " " + content
                    current_segment["end_time"] = end_time
                else:
                    if current_segment:
                        transformed_segments.append(current_segment)
                    current_segment = {
                        "text": content,
                        "speaker": speaker,
                        "start_time": start_time,
                        "end_time": end_time,
                    }

    if current_segment:
        transformed_segments.append(current_segment)
    return transformed_segments

@app.websocket("/ws/rt-audio")
async def websocket_rt_audio(
    websocket: WebSocket, caseID: str = Query(...), interviewID: str = Query(...)
):
    """
    Main WebSocket handler for real-time audio relay, transcription, and uploading.

    Args:
        websocket (WebSocket): FastAPI WebSocket connection.
        caseID (str): Case ID.
        interviewID (str): Interview ID.
    """
    await websocket.accept()
    audio_buffer = bytearray()
    session_id = f"{caseID}_{interviewID}_{id(websocket)}"
    print(f"🎤 [WS] New client connected: session={session_id}")

    seq_no = 0
    all_segments = []
    relay_opened = False
    timeout_task = None
    audio_url = None  # Will be set after upload

    try:
        print(f"[DEBUG] [{session_id}] Opening Speechmatics relay...")
        async with websockets.connect(
            SM_WS_URL,
            additional_headers={"Authorization": f"Bearer {SPEECHMATICS_API_KEY}"},
        ) as sm_ws:
            relay_opened = True
            print(f"[DEBUG] [{session_id}] Connected to Speechmatics.")

            # 1. Send StartRecognition
            start_msg = {
                "message": "StartRecognition",
                "audio_format": {
                    "type": "raw",
                    "encoding": "pcm_s16le",
                    "sample_rate": SAMPLE_RATE,
                },
                "transcription_config": {
                    "language": LANG,
                    "operating_point": "enhanced",
                    "diarization": "speaker",
                    "enable_partials": True,
                    "max_delay": 2.0,
                },
            }
            await sm_ws.send(json.dumps(start_msg))

            # 2. Wait for RecognitionStarted
            while True:
                msg = await sm_ws.recv()
                try:
                    msg_obj = json.loads(msg)
                    if msg_obj.get("message") == "RecognitionStarted":
                        print(
                            f"[DEBUG] [{session_id}] Speechmatics RecognitionStarted."
                        )
                        break
                    else:
                        await safe_websocket_send(websocket, msg)
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Error parsing initial message: {e}")

            async def wait_for_end_of_transcript():
                try:
                    await asyncio.sleep(30)
                    print(f"[DEBUG] [{session_id}] Timeout waiting for EndOfTranscript, processing anyway")
                except asyncio.CancelledError:
                    # Task was cancelled on normal completion; no problem.
                    print(f"[DEBUG] [{session_id}] Timeout task cancelled cleanly")
                    return


            # --- Main relay coroutines ---
            async def client_to_sm():
                """
                Relay audio data from client to Speechmatics, save/upload the audio buffer after stream ends.
                """
                nonlocal seq_no, audio_url
                try:
                    while True:
                        ws_msg = await websocket.receive()
                        if ws_msg["type"] == "websocket.disconnect":
                            print(
                                f"[DEBUG] [{session_id}] WebSocket disconnect received"
                            )
                            await websocket.close()
                            break
                        elif "bytes" in ws_msg and ws_msg["bytes"]:
                            audio_buffer.extend(ws_msg["bytes"])
                            await sm_ws.send(ws_msg["bytes"])
                            seq_no += 1
                        elif "text" in ws_msg and ws_msg["text"]:
                            if ws_msg["text"] == "END":
                                await sm_ws.send(
                                    json.dumps(
                                        {
                                            "message": "EndOfStream",
                                            "last_seq_no": seq_no,
                                        }
                                    )
                                )
                                break

                    # Save and upload audio buffer after recording stops
                    if audio_buffer:
                        with tempfile.NamedTemporaryFile(
                            delete=False, suffix=".wav"
                        ) as temp_wav:
                            temp_wav_filename = temp_wav.name
                        save_pcm_to_wav(
                            audio_buffer,
                            temp_wav_filename,
                            sample_rate=SAMPLE_RATE,
                            nchannels=1,
                            sampwidth=2,
                        )
                        storage_path = f"{interviewID}.wav"
                        upload_response = upload_wav_to_supabase(
                            supabase, "recordings", storage_path, temp_wav_filename
                        )
                        print(f"[DEBUG] Supabase upload response: {upload_response}")

                        signed_url_resp = supabase.storage.from_(
                            "recordings"
                        ).create_signed_url(storage_path, 3600)
                        audio_url = signed_url_resp.get("signedURL")

                        delete_temp_file(temp_wav_filename)
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Client audio relay error: {e}")

            async def sm_to_client():
                """
                Relay messages from Speechmatics to client, generate summary, and send final messages.

                Returns:
                    None
                """
                nonlocal all_segments, timeout_task, audio_url
                try:
                    async for msg in sm_ws:
                        try:
                            msg_obj = json.loads(msg)
                            message_type = msg_obj.get("message")
                            if message_type == "AddTranscript":
                                segments = transform_speechmatics_to_desired_format(
                                    msg_obj
                                )
                                if segments:
                                    all_segments.extend(segments)
                                partial_response = {
                                    "message": "PartialTranscript",
                                    "segments": segments or [],
                                    "all_segments": all_segments,
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(partial_response)
                                )
                            elif message_type == "AddPartialTranscript":
                                segments = transform_speechmatics_to_desired_format(
                                    msg_obj
                                )
                                partial_response = {
                                    "message": "AddPartialTranscript",
                                    "segments": segments or [],
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(partial_response)
                                )
                            elif message_type == "EndOfTranscript":
                                final_response = {
                                    "message": "FinalTranscript",
                                    "segments": all_segments,
                                }
                                await safe_websocket_send(
                                    websocket, json.dumps(final_response)
                                )
                                print(
                                    f"[DEBUG] [{session_id}] Sent FinalTranscript ({len(all_segments)} segments)"
                                )
                                if all_segments:
                                    await safe_websocket_send(
                                        websocket,
                                        json.dumps({"message": "SummaryGenerating"}),
                                    )
                                    try:
                                        summary = await generate_summary(all_segments)
                                        statement_info = await extract_statement_info(all_segments)
                                        summary_response = {
                                            "message": "FinalSummary",
                                            "summary": summary,
                                            "audio_url": audio_url,
                                            "statement_info": statement_info,
                                            "metadata": {
                                                "total_segments": len(all_segments),
                                                "speakers": list(
                                                    set(
                                                        s.get("speaker", "Unknown")
                                                        for s in all_segments
                                                    )
                                                ),
                                                "duration": (
                                                    max(
                                                        s.get("end_time", 0)
                                                        for s in all_segments
                                                    )
                                                    if all_segments
                                                    else 0
                                                ),
                                            },
                                        }
                                        await safe_websocket_send(
                                            websocket, json.dumps(summary_response)
                                        )
                                        print(
                                            f"[DEBUG] [{session_id}] Sent FinalSummary to client."
                                        )
                                    except Exception as e:
                                        print(
                                            f"[ERROR] [{session_id}] Summary generation failed: {e}"
                                        )
                                        error_response = {
                                            "message": "FinalSummary",
                                            "summary": f"Summary generation failed: {str(e)}",
                                            "error": True,
                                        }
                                        await safe_websocket_send(
                                            websocket, json.dumps(error_response)
                                        )
                                if timeout_task:
                                    timeout_task.cancel()
                                break
                            else:
                                await safe_websocket_send(websocket, msg)
                        except json.JSONDecodeError:
                            await safe_websocket_send(websocket, msg)
                        except Exception as e:
                            print(
                                f"[ERROR] [{session_id}] Error processing Speechmatics message: {e}"
                            )
                except Exception as e:
                    print(f"[ERROR] [{session_id}] Speechmatics relay error: {e}")
                finally:
                    try:
                        await sm_ws.close()
                    except Exception as e:
                        print(f"[ERROR] Error closing Speechmatics relay: {e}")

            timeout_task = asyncio.create_task(wait_for_end_of_transcript())
            await asyncio.gather(client_to_sm(), sm_to_client(), timeout_task)
    except Exception as e:
        print(f"[ERROR] [{session_id}] WebSocket handler error: {e}")
    finally:
        print(f"[DEBUG] [{session_id}] Closing WebSocket connection.")
        try:
            await websocket.close()
            print(f"[DEBUG] [{session_id}] WebSocket connection closed successfully.")
        except Exception as e:
            print(f"[ERROR] [{session_id}] Error closing WebSocket: {e}")

        print(f"[DEBUG] [{session_id}] Closing Speechmatics WebSocket.")
        try:
            await sm_ws.close()
            print(f"[DEBUG] [{session_id}] Speechmatics WebSocket closed successfully.")
        except Exception as e:
            print(f"[ERROR] [{session_id}] Error closing Speechmatics WebSocket: {e}")


@app.get("/", response_class=HTMLResponse)
def root():
    """
    Root endpoint with simple HTML instructions.
    """
    return """
    <h2>Speechmatics Real-Time Diarization WebSocket Proxy</h2>
    <p>Connect your frontend WebSocket client to <code>/ws/rt-audio?caseID=test&interviewID=demo</code>.</p>
    """


@app.get("/audio-url")
def get_audio_url(interview_id: str):
    """
    Endpoint to get a signed audio URL for an interview's audio file.

    Args:
        interview_id (str): Interview ID.

    Returns:
        dict: Signed audio URL.
    """
    storage_path = f"{interview_id}.wav"
    signed_url_resp = supabase.storage.from_("recordings").create_signed_url(
        storage_path, 3600
    )
    audio_url = signed_url_resp.get("signedURL")
    return {"audio_url": audio_url}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="0.0.0.0", port=8081, reload=False)
