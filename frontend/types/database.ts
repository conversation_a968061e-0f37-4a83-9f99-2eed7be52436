// Database types for FIU Witness Interview System
// Generated from PostgreSQL schema

export type UserRole = 'officer' | 'admin' | 'supervisor';
export type CaseStatus = 'In Progress' | 'Completed';
export type InterviewStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
export type WitnessType = 'Resident' | 'Neighbor' | 'Passerby' | 'Business Owner' | 'Emergency Responder';
export type InterviewEnvironment = 'controlled' | 'field';

// Environment display mapping
export const INTERVIEW_ENVIRONMENT_LABELS: Record<InterviewEnvironment, string> = {
  controlled: 'Controlled Environment (Station/Office)',
  field: 'Field Environment (On-Scene)',
};
export type TranscriptionStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type ExportType = 'pdf' | 'docx';

// Database table interfaces
export interface DatabaseUser {
  id: string;
  email: string;
  full_name: string;
  badge_number: string | null;
  department: string | null;
  role: UserRole;
  is_active: boolean;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseCase {
  id: string;
  incident_location: string;
  incident_date: string; // ISO date string
  incident_time: string; // HH:MM format
  assigned_officer_id: string;
  status: CaseStatus;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseInterview {
  id: string;
  case_id: string;
  interviewing_officer_id: string;
  witness_name: string;
  witness_type: string;
  witness_contact: string | null;
  interview_environment: InterviewEnvironment | null;
  status: InterviewStatus;
  start_time: string | null; // ISO datetime string
  end_time: string | null; // ISO datetime string
  duration_seconds: number | null;
  recording_path: string | null;
  witness_signature_path: string | null;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseTranscription {
  id: string;
  interview_id: string;
  transcription_data: TranscriptionDataStructure; // JSONB data
  language: string;
  confidence_score: number | null;
  processing_status: TranscriptionStatus;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

// Structure for the JSONB transcription_data field
export interface TranscriptionDataStructure {
  speakers: Array<{
    id: string; // 'officer' | 'witness' | 'S1' | 'S2' | etc.
    name: string;
    type: string; // 'officer' | 'witness' | 'speaker'
    color?: string; // UI color for speaker identification
  }>;
  segments: Array<{
    speaker: string; // 'officer' | 'witness'
    timestamp: string; // MM:SS format
    text: string;
    confidence?: number;
  }>;
  metadata?: {
    total_duration?: number;
    segment_count?: number;
    average_confidence?: number;
  };
}

// Structure for the JSONB statement_form field
export interface StatementFormData {
  // Personal Information
  personName: string;
  guardianName?: string;
  scdfIncidentNo: string;
  incidentDate: string; // ISO date string
  gender: string;
  age: number;
  dateOfBirth: string; // ISO date string
  maritalStatus: string;

  // Identification
  identificationType: 'NRIC' | 'FIN' | 'Passport' | 'Work Permit' | 'Employment Pass';
  identificationNumber: string;
  nationality: string;

  // Contact Information
  residentialAddress: string;
  occupation: string;
  employmentAddress: string;
  telephoneNumber: string;
  emailAddress: string;
  guardianTelephoneNumber?: string;

  // Statement Recording Details
  recordingStartTime: string; // HH:MM format
  recordingEndTime: string; // HH:MM format
  recordingDate: string; // ISO date string
  recordingPlace: string;
  languageSpoken: string;

  // Interpreter Information (if applicable)
  interpreterName?: string;
  interpreterContact?: string;
  interpreterSignature?: string; // Base64 signature data or uploaded URL

  // Recording Officer Information
  recordingOfficerName: string;
  recordingOfficerSignature?: string; // Base64 signature data or uploaded URL

  // Statement Maker Signatures
  statementMakerName: string;
  statementMakerSignature?: string; // Base64 signature data or uploaded URL

  // SCDF Interviewer Signatures
  scdfInterviewerName: string;
  scdfInterviewerSignature?: string; // Base64 signature data or uploaded URL
}

export interface DatabaseStatement {
  id: string;
  interview_id: string;
  content: string;
  officer_notes: string | null;
  statement_form: StatementFormData | null;
  version: number;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseAudioRecording {
  id: string;
  interview_id: string;
  file_path: string;
  file_size: number | null;
  duration_seconds: number | null;
  format: string | null;
  sample_rate: number | null;
  channels: number | null;
  created_at: string; // ISO datetime string
}

export interface DatabaseExportLog {
  id: string;
  interview_id: string;
  export_type: ExportType;
  file_path: string | null;
  exported_by_user_id: string | null;
  created_at: string; // ISO datetime string
}

// API request/response types
export interface CreateUserRequest {
  email: string;
  full_name: string;
  badge_number?: string;
  department?: string;
  role?: UserRole;
}

export interface UpdateUserRequest {
  email?: string;
  full_name?: string;
  badge_number?: string;
  department?: string;
  role?: UserRole;
  is_active?: boolean;
}

export interface CreateCaseRequest {
  incident_location: string;
  incident_date: string;
  incident_time: string;
  assigned_officer_id: string;
}

export interface UpdateCaseRequest {
  incident_location?: string;
  incident_date?: string;
  incident_time?: string;
  assigned_officer_id?: string;
  status?: CaseStatus;
}

export interface CreateInterviewRequest {
  interviewing_officer_id: string;
  witness: {
    name: string;
    type: string;
    contact: string;
    environment?: InterviewEnvironment;
  };
  recording_path?: string;
  witness_signature_path?: string;
}

export interface UpdateInterviewRequest {
  interviewing_officer_id?: string;
  witness?: {
    name: string;
    type: string;
    contact: string;
    environment?: InterviewEnvironment;
  };
  status?: InterviewStatus;
  start_time?: string;
  end_time?: string;
  duration_seconds?: number;
}

export interface CreateTranscriptionRequest {
  transcription_data: TranscriptionDataStructure;
  language?: string;
  confidence_score?: number;
}

export interface UpdateStatementRequest {
  content: string;
  officer_notes?: string;
  statement_form?: StatementFormData;
}

// Frontend model interfaces (transformed from database types)
export interface User {
  id: string;
  email: string;
  fullName: string;
  badgeNumber?: string;
  department?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Case {
  id: string;
  incidentLocation: string;
  incidentDate: string;
  incidentTime: string;
  assignedOfficerId: string;
  assignedOfficer?: User; // Populated when joined
  status: CaseStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface Witness {
  name: string;
  type: string;
  contact: string;
  environment?: InterviewEnvironment;
}

export interface Interview {
  id: string;
  caseId: string;
  interviewingOfficerId: string;
  interviewingOfficer?: User; // Populated when joined
  witness: Witness;
  status: InterviewStatus;
  startTime?: Date;
  endTime?: Date;
  duration?: number; // in seconds
  recordingPath?: string;
  witnessSignaturePath?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Transcription {
  id: string;
  interviewId: string;
  transcriptionData: TranscriptionDataStructure;
  language: string;
  confidenceScore?: number;
  processingStatus: TranscriptionStatus;
  createdAt: Date;
  updatedAt: Date;
}

// Simplified speaker and segment interfaces for frontend use
export interface TranscriptSpeaker {
  id: string; // 'officer' | 'witness' | 'S1' | 'S2' | etc.
  name: string;
  type: string;
  color?: string; // UI color for speaker identification
}

export interface TranscriptSegment {
  speaker: string; // 'officer' | 'witness'
  timestamp: string;
  text: string;
  confidence?: number;
}

export interface TranscriptData {
  speakers: TranscriptSpeaker[];
  segments: TranscriptSegment[];
  metadata?: {
    totalDuration?: number;
    segmentCount?: number;
    averageConfidence?: number;
  };
}

export interface Statement {
  id: string;
  interviewId: string;
  content: string;
  officerNotes?: string;
  statementForm?: StatementFormData;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AudioRecording {
  id: string;
  interviewId: string;
  filePath: string;
  fileSize?: number;
  duration?: number;
  format?: string;
  sampleRate?: number;
  channels?: number;
  createdAt: Date;
}

export interface ExportLog {
  id: string;
  interviewId: string;
  exportType: ExportType;
  filePath?: string;
  exportedByUserId?: string;
  exportedByUser?: User; // Populated when joined
  createdAt: Date;
}

// WebSocket message types
export interface TranscriptionMessage {
  type: 'transcription' | 'control' | 'error';
  data: TranscriptSegment | ControlMessage | ErrorMessage;
}

export interface ControlMessage {
  action: 'start' | 'pause' | 'resume' | 'stop';
  timestamp: string;
}

export interface ErrorMessage {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// Note: API response types removed - using direct Supabase service calls instead

// Utility types for forms and state management
export interface CaseFormData {
  caseId: string;
  incidentLocation: string;
  incidentDate: string;
  incidentTime: string;
}

export interface WitnessFormData {
  witnessName: string;
  witnessType: string;
  witnessContact: string;
  interviewEnvironment: InterviewEnvironment;
}





// Database transformation utilities type
export type DatabaseTransformer<TDatabase, TFrontend> = (db: TDatabase) => TFrontend;

// Supabase specific types
export interface SupabaseResponse<T> {
  data: T | null;
  error: {
    message: string;
    details: string;
    hint: string;
    code: string;
  } | null;
}

export interface SupabaseRealtimePayload<T> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any[];
}

// Query filter types for API endpoints
export interface CaseFilters {
  status?: CaseStatus;
  officer?: string;
  limit?: number;
  offset?: number;
}

export interface InterviewFilters {
  caseId?: string;
  status?: InterviewStatus;
  limit?: number;
  offset?: number;
}

// File upload types
export interface AudioUploadMetadata {
  interviewId: string;
  fileName: string;
  fileSize: number;
  duration?: number;
  format: string;
}

export interface ExportOptions {
  includeTranscript: boolean;
  includeAudio: boolean;
  includeOfficerNotes: boolean;
  format: ExportType;
}
