import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a server-side client with service key
const getServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceKey = process.env.SUPABASE_SERVICE_KEY
  
  if (!supabaseUrl || !serviceKey) {
    throw new Error('Missing Supabase configuration')
  }
  
  return createClient(supabaseUrl, serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const signatureUrl = searchParams.get('url')
    
    if (!signatureUrl) {
      return NextResponse.json(
        { error: 'Missing signature URL' },
        { status: 400 }
      )
    }

    const supabase = getServiceClient()
    const bucketName = process.env.SIGNATURE_BUCKET_NAME
    
    if (!bucketName) {
      return NextResponse.json(
        { error: 'SIGNATURE_BUCKET_NAME environment variable is not set' },
        { status: 500 }
      )
    }

    // Extract filename from URL
    const url = new URL(signatureUrl)
    const pathParts = url.pathname.split('/')
    const filename = pathParts[pathParts.length - 1]

    // Get signed URL for private access
    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filename, 3600) // 1 hour expiry

    if (error) {
      console.error('Error creating signed URL:', error)
      return NextResponse.json(
        { error: 'Failed to access signature image' },
        { status: 500 }
      )
    }

    if (!data?.signedUrl) {
      return NextResponse.json(
        { error: 'Failed to generate signed URL' },
        { status: 500 }
      )
    }

    // Fetch the image and return it as a blob
    const imageResponse = await fetch(data.signedUrl)
    
    if (!imageResponse.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch signature image' },
        { status: 500 }
      )
    }

    const imageBlob = await imageResponse.blob()
    
    return new NextResponse(imageBlob, {
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    })

  } catch (error) {
    console.error('Error fetching signature image:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
