"use client"

import { useState } from "react"
import { use<PERSON><PERSON> } from 'jotai'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Loader2, PenTool } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { InterviewService, TranscriptionService, StatementService } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import { ClientSignatureOverlay } from "@/components/client-signature-overlay"
import { SignatureService } from "@/lib/signature-service"
import { StatementForm } from "@/components/statement-form"
import {
  currentCaseAtom,
  currentWitnessAtom,
  transcriptDataAtom,
  summaryData<PERSON>tom,
  officerNotesAtom,
  recordingStart<PERSON><PERSON><PERSON><PERSON>,
  recordingEnd<PERSON><PERSON><PERSON><PERSON>,
  currentInterview<PERSON>tom,
  export<PERSON>ata<PERSON><PERSON>,
  recording<PERSON><PERSON><PERSON><PERSON>,
  saveR<PERSON><PERSON>ing<PERSON><PERSON>
} from "@/store/atoms"
import type { Screen } from "@/app/page"
import type { StatementFormData } from "@/types/database"

interface TranscriptionScreenProps {
  onNavigate: (screen: Screen) => void
}

export function TranscriptionScreen({ onNavigate }: TranscriptionScreenProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [isProcessing, setIsProcessing] = useState(false)
  const [isSignatureOverlayOpen, setIsSignatureOverlayOpen] = useState(false)
  const [witnessSignature, setWitnessSignature] = useState<string | null>(null)
  const [statementFormData, setStatementFormData] = useState<StatementFormData | null>(null)

  // Pre-populate form data based on current case and witness
  const getInitialFormData = (): StatementFormData | undefined => {
    if (!currentCase || !currentWitness || !user) return undefined

    const recordingStart = recordingStartTime ? new Date(recordingStartTime) : null
    const recordingEnd = recordingEndTime ? new Date(recordingEndTime) : null

    return {
      personName: currentWitness.name,
      guardianName: "",
      scdfIncidentNo: currentCase.id,
      incidentDate: currentCase.incidentDate,
      gender: "",
      age: 0,
      dateOfBirth: "",
      maritalStatus: "",
      identificationType: "NRIC",
      identificationNumber: "",
      nationality: "",
      residentialAddress: "",
      occupation: "",
      employmentAddress: "",
      telephoneNumber: currentWitness.contact || "",
      emailAddress: "",
      guardianTelephoneNumber: "",
      recordingStartTime: recordingStart ? recordingStart.toTimeString().slice(0, 5) : "",
      recordingEndTime: recordingEnd ? recordingEnd.toTimeString().slice(0, 5) : "",
      recordingDate: recordingStart ? recordingStart.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      recordingPlace: currentCase.incidentLocation,
      languageSpoken: "English",
      interpreterName: "",
      interpreterContact: "",
      interpreterSignature: "",
      recordingOfficerName: user.fullName,
      recordingOfficerSignature: "",
      statementMakerName: currentWitness.name,
      statementMakerSignature: "",
      scdfInterviewerName: user.fullName,
      scdfInterviewerSignature: "",
    }
  }
  const [showStatementForm, setShowStatementForm] = useState(false)
  const [createdInterviewId, setCreatedInterviewId] = useState<string | null>(null)

  const [currentCase] = useAtom(currentCaseAtom)
  const [currentWitness] = useAtom(currentWitnessAtom)
  const [transcriptData] = useAtom(transcriptDataAtom)
  const [summaryData] = useAtom(summaryDataAtom)
  const [officerNotes, setOfficerNotes] = useAtom(officerNotesAtom)
  const [recordingStartTime] = useAtom(recordingStartTimeAtom)
  const [recordingEndTime] = useAtom(recordingEndTimeAtom)
  const [, setCurrentInterview] = useAtom(currentInterviewAtom)
  const [, setExportData] = useAtom(exportDataAtom)
  const [recordingUrl] = useAtom(recordingUrlAtom)
  const [saveRecording] = useAtom(saveRecordingAtom)

  const currentDate =
    new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }) +
    " at " +
    new Date().toLocaleTimeString("en-US")

  const handleSignatureSave = async (signatureData: string) => {
    try {
      setWitnessSignature(signatureData)
      toast({
        title: "Signature Saved",
        description: "Witness signature has been captured successfully.",
      })
    } catch (error) {
      console.error('Error saving signature:', error)
      toast({
        title: "Error Saving Signature",
        description: "Failed to save the witness signature. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleApproveAndExport = async () => {
    if (!user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to approve and export interviews.",
        variant: "destructive",
      })
      return
    }

    if (!currentCase) {
      toast({
        title: "Missing Case Information",
        description: "No case information found. Please start from the case selection.",
        variant: "destructive",
      })
      return
    }

    if (!currentWitness) {
      toast({
        title: "Missing Witness Information",
        description: "No witness information found. Please complete witness setup.",
        variant: "destructive",
      })
      return
    }

    if (!transcriptData || !summaryData) {
      toast({
        title: "Missing Interview Data",
        description: "No transcription or summary data found. Please complete the recording.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // Calculate duration from actual recording times
      const startTime = recordingStartTime
      const endTime = recordingEndTime || Date.now()
      const durationSeconds = startTime ? Math.floor((endTime - startTime) / 1000) : 0

      // Upload signature if available
      let signaturePath: string | undefined = undefined
      if (witnessSignature) {
        try {
          // Generate a temporary interview ID for signature upload
          const tempInterviewId = `temp_${Date.now()}`
          signaturePath = await SignatureService.uploadSignature(witnessSignature, tempInterviewId)
        } catch (error) {
          console.error('Error uploading signature:', error)
          toast({
            title: "Signature Upload Failed",
            description: "Failed to upload witness signature. Proceeding without signature.",
            variant: "destructive",
          })
        }
      }

      // Step 1: Create the interview with actual timestamps, recording path, and signature
      const interviewData = {
        interviewing_officer_id: user.id,
        witness: {
          name: currentWitness.name,
          type: currentWitness.type,
          contact: currentWitness.contact,
          environment: currentWitness.environment,
        },
        recording_path: saveRecording && recordingUrl ? recordingUrl : undefined,
        witness_signature_path: signaturePath,
      }

      const interview = await InterviewService.createInterview(
        currentCase.id,
        interviewData
      )

      // Step 2: Update interview with actual recording timestamps and duration
      const updatedInterview = await InterviewService.updateInterview(interview.id, {
        start_time: startTime ? new Date(startTime).toISOString() : undefined,
        end_time: new Date(endTime).toISOString(),
        duration_seconds: durationSeconds,
      })

      // Step 3: Create the transcription
      // With the new speaker system, we pass the full transcript data with all speakers
      await TranscriptionService.createTranscription(
        updatedInterview.id,
        transcriptData
      )

      // Step 4: Create the statement with user-entered officer notes
      const officerNotesText = officerNotes || `Interview conducted on ${currentDate} by ${user.fullName}.`

      const createdStatement = await StatementService.createStatement(
        updatedInterview.id,
        summaryData,
        officerNotesText,
        statementFormData || undefined
      )

      toast({
        title: "Interview Saved Successfully",
        description: `Interview ID: ${updatedInterview.id}. The interview, transcription, and statement have been saved to the database.`,
      })

      // Store the created interview ID for form usage
      setCreatedInterviewId(updatedInterview.id)

      // Prepare export data for the export screen
      setCurrentInterview(updatedInterview)
      setExportData({
        interview: updatedInterview,
        case: currentCase,
        officer: user,
        transcriptData: transcriptData,
        statement: createdStatement,
      })

      // Navigate to export screen
      onNavigate("export-screen")

    } catch (error) {
      console.error('Error saving interview:', error)
      toast({
        title: "Error Saving Interview",
        description: error instanceof Error ? error.message : "An unexpected error occurred while saving the interview.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <h2 className="text-2xl font-semibold">Statement Review</h2>
        {/* <Button variant="outline" size="sm" onClick={() => onNavigate("recording-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Recording
        </Button> */}
      </div>

      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Interview Transcript</CardTitle>
          <p className="text-sm text-muted-foreground">Recorded: {currentDate}</p>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-96 overflow-y-auto p-4">
            {transcriptData?.segments.map((segment: any, index: number) => {
              const speaker = transcriptData?.speakers.find((s: any) => s.id === segment.speaker)
              return (
                <div key={index} className="flex gap-4 mb-4">
                  <div
                    className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                    style={{ borderRightColor: speaker?.color }}
                  >
                    {speaker?.name}
                    <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                  </div>
                  <div className="flex-1 text-sm leading-relaxed">{segment.text}</div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Auto-Generated Summary</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="markdown-content">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children }) => (
                  <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-xl font-semibold mb-3 mt-6 text-gray-900 dark:text-gray-100">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-lg font-medium mb-2 mt-4 text-gray-900 dark:text-gray-100">{children}</h3>
                ),
                p: ({ children }) => (
                  <p className="mb-3 text-gray-700 dark:text-gray-300 leading-relaxed">{children}</p>
                ),
                ul: ({ children }) => <ul className="list-disc pl-6 mb-4 space-y-1">{children}</ul>,
                li: ({ children }) => <li className="text-gray-700 dark:text-gray-300">{children}</li>,
                strong: ({ children }) => (
                  <strong className="font-semibold text-gray-900 dark:text-gray-100">{children}</strong>
                ),
              }}
            >
              {summaryData}
            </ReactMarkdown>
          </div>
        </CardContent>
      </Card>
      
      {/* Officer Notes Section */}
      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Officer Notes</CardTitle>
          <p className="text-sm text-muted-foreground">Add any additional observations or notes about the interview</p>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-2">
            <Label htmlFor="officer-notes">Additional Notes</Label>
            <Textarea
              id="officer-notes"
              value={officerNotes || ""}
              onChange={(e) => setOfficerNotes(e.target.value)}
              rows={4}
              placeholder="Enter any additional notes, observations, or important details about the interview..."
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      {/* Witness Signature Section */}
      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Witness Signature</CardTitle>
          <p className="text-sm text-muted-foreground">Collect the witness signature to confirm the statement</p>
        </CardHeader>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <PenTool className="w-5 h-5 text-gray-600" />
              <div>
                <p className="font-medium">
                  {witnessSignature ? "Signature Collected" : "No Signature"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {witnessSignature ? "Click to view or update signature" : "Click to collect witness signature"}
                </p>
              </div>
            </div>
            <Button
              variant={witnessSignature ? "outline" : "default"}
              onClick={() => setIsSignatureOverlayOpen(true)}
              className="flex items-center gap-2"
            >
              <PenTool className="w-4 h-4" />
              {witnessSignature ? "View Signature" : "Witness Signature"}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Statement Form Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Statement Form
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowStatementForm(!showStatementForm)}
            >
              {showStatementForm ? "Hide Form" : "Show Form"}
            </Button>
          </CardTitle>
        </CardHeader>
        {showStatementForm && (
          <CardContent>
            <StatementForm
              initialData={statementFormData || getInitialFormData()}
              onSubmit={setStatementFormData}
              interviewId={createdInterviewId || undefined}
              isLoading={false}
              showSaveButton={false}
            />
          </CardContent>
        )}
      </Card>

      <div className="flex gap-3">
        <Button
          variant="secondary"
          className="flex-1"
          onClick={() => onNavigate("statement-edit")}
          disabled={isProcessing}
        >
          Edit Statement
        </Button>
        <Button
          className="flex-1"
          onClick={handleApproveAndExport}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            "Approve & Export"
          )}
        </Button>
      </div>

      {/* Signature Overlay */}
      <ClientSignatureOverlay
        isOpen={isSignatureOverlayOpen}
        onClose={() => setIsSignatureOverlayOpen(false)}
        onSave={handleSignatureSave}
        existingSignature={witnessSignature || undefined}
        title="Witness Signature"
      />
    </div>
  )
}
