"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { ClientSignatureOverlay } from "@/components/client-signature-overlay"
import { SignatureService } from "@/lib/signature-service"
import { PenTool, Save, Image as ImageIcon } from "lucide-react"
import type { StatementFormData } from "@/types/database"

// Helper function to check if signature is a data URL or uploaded URL
function isDataURL(str: string): boolean {
  return str.startsWith('data:image/')
}

// Component to display signature images
interface SignatureDisplayProps {
  signatureUrl?: string
  label: string
}

// Component for signature preview with retry option
interface SignaturePreviewProps {
  signatureData?: string
  label: string
  onRetry: () => void
}

function SignaturePreview({ signatureData, label, onRetry }: SignaturePreviewProps) {
  if (!signatureData) {
    return (
      <div className="mt-3">
        <Label>{label}</Label>
        <div className="flex items-center justify-between gap-2 mt-1 p-3 border border-dashed border-gray-300 rounded-md bg-gray-50">
          <div className="flex items-center gap-2">
            <ImageIcon className="w-4 h-4 text-gray-400" />
            <span className="text-sm text-gray-500">No signature captured</span>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onRetry}
          >
            <PenTool className="w-4 h-4 mr-2" />
            Add Signature
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="mt-3">
      <Label>{label}</Label>
      <div className="mt-1 p-2 border border-gray-200 rounded-md bg-white">
        <img
          src={signatureData}
          alt={label}
          className="max-w-full h-auto max-h-24 object-contain border border-gray-100 rounded"
        />
        <div className="mt-2 flex items-center justify-between">
          <span className="text-xs text-green-600">✓ Signature captured</span>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onRetry}
          >
            <PenTool className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    </div>
  )
}

function SignatureDisplay({ signatureUrl, label }: SignatureDisplayProps) {
  if (!signatureUrl) {
    return (
      <div className="mt-3">
        <Label>{label}</Label>
        <div className="flex items-center gap-2 mt-1 p-3 border border-dashed border-gray-300 rounded-md bg-gray-50">
          <ImageIcon className="w-4 h-4 text-gray-400" />
          <span className="text-sm text-gray-500">No signature provided</span>
        </div>
      </div>
    )
  }

  return (
    <div className="mt-3">
      <Label>{label}</Label>
      <div className="mt-1 p-2 border border-gray-200 rounded-md bg-white">
        <img
          src={signatureUrl}
          alt={label}
          className="max-w-full h-auto max-h-24 object-contain border border-gray-100 rounded"
          onError={(e) => {
            const target = e.target as HTMLImageElement
            target.style.display = 'none'
            const parent = target.parentElement
            if (parent) {
              parent.innerHTML = `
                <div class="flex items-center gap-2 p-2 text-gray-500">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                  <span class="text-sm">Unable to load signature</span>
                </div>
              `
            }
          }}
        />
        <div className="mt-1 text-xs text-gray-500">
          {isDataURL(signatureUrl) ? 'Signature captured' : 'Signature uploaded'}
        </div>
      </div>
    </div>
  )
}

interface StatementFormProps {
  initialData?: StatementFormData
  onSubmit: (data: StatementFormData) => void
  interviewId?: string
  isLoading?: boolean
  showSaveButton?: boolean
  readOnlySignatures?: boolean
  allowSignaturePreview?: boolean  // New prop to enable preview/retry functionality
  className?: string
}

export function StatementForm({
  initialData,
  onSubmit,
  interviewId,
  isLoading = false,
  showSaveButton = false,
  readOnlySignatures = false,
  allowSignaturePreview = false,
  className = ""
}: StatementFormProps) {
  const [isSignatureOverlayOpen, setIsSignatureOverlayOpen] = useState(false)
  const [currentSignatureField, setCurrentSignatureField] = useState<string>("")

  const form = useForm<StatementFormData>({
    defaultValues: initialData || {
      personName: "",
      guardianName: "",
      scdfIncidentNo: "",
      incidentDate: "",
      gender: "",
      age: null,
      dateOfBirth: "",
      maritalStatus: "",
      identificationType: "NRIC",
      identificationNumber: "",
      nationality: "",
      residentialAddress: "",
      occupation: "",
      employmentAddress: "",
      telephoneNumber: "",
      emailAddress: "",
      guardianTelephoneNumber: "",
      recordingStartTime: "",
      recordingEndTime: "",
      recordingDate: "",
      recordingPlace: "",
      languageSpoken: "",
      interpreterName: "",
      interpreterContact: "",
      interpreterSignature: "",
      recordingOfficerName: "",
      recordingOfficerSignature: "",
      statementMakerName: "",
      statementMakerSignature: "",
      scdfInterviewerName: "",
      scdfInterviewerSignature: "",
    }
  })

  // Update form when initialData changes (e.g., when signatures are added)
  useEffect(() => {
    if (initialData) {
      console.log('Resetting form with new initial data:', initialData)
      form.reset(initialData)
    }
  }, [initialData, form])

  const handleSignatureClick = (fieldName: string) => {
    console.log(`Opening signature overlay for: ${fieldName}`)
    const currentSignature = form.getValues(fieldName as keyof StatementFormData) as string
    console.log(`Current signature for ${fieldName}:`, currentSignature ? currentSignature.substring(0, 50) + '...' : 'empty')

    setCurrentSignatureField(fieldName)
    setIsSignatureOverlayOpen(true)
  }

  const handleSignatureSave = async (signatureData: string) => {
    if (!currentSignatureField) return

    console.log(`Saving signature for ${currentSignatureField}:`, signatureData.substring(0, 50) + '...')

    // Always store base64 data for preview/retry functionality
    // Upload will happen later when form is submitted (on approve)
    form.setValue(currentSignatureField as keyof StatementFormData, signatureData)

    // Trigger form change to update the parent component
    const currentFormData = form.getValues()
    console.log(`Form data after signature save:`, currentFormData)
    onSubmit(currentFormData)

    setIsSignatureOverlayOpen(false)
    setCurrentSignatureField("")
  }

  const handleFormSubmit = async (data: StatementFormData) => {
    // If allowSignaturePreview is true, upload signatures now before submitting
    if (allowSignaturePreview && interviewId) {
      try {
        const formDataWithSignatures = { ...data }

        // Upload signatures if they exist and are base64 data URLs
        const signatureFields = [
          { field: 'interpreterSignature', type: 'interpreter' },
          { field: 'recordingOfficerSignature', type: 'recording_officer' },
          { field: 'statementMakerSignature', type: 'statement_maker' },
          { field: 'scdfInterviewerSignature', type: 'scdf_interviewer' }
        ] as const

        for (const { field, type } of signatureFields) {
          const signatureData = data[field]
          if (signatureData && signatureData.startsWith('data:image/')) {
            try {
              const uploadedUrl = await SignatureService.uploadStatementSignature(
                signatureData,
                interviewId,
                type
              )
              formDataWithSignatures[field] = uploadedUrl
            } catch (error) {
              console.error(`Failed to upload ${type} signature:`, error)
              // Continue with the original data if upload fails
            }
          }
        }

        onSubmit(formDataWithSignatures)
      } catch (error) {
        console.error('Error processing form submission:', error)
        onSubmit(data)
      }
    } else {
      // Signatures are already uploaded when captured, so just submit the form data
      onSubmit(data)
    }
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Personal Information Section */}
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="personName"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Name (including aliases, if any) of person making statement</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="guardianName"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Name and Identification number of Guardian</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="scdfIncidentNo"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>SCDF Incident No.</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="incidentDate"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Date of Incident</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Gender</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="age"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Age</FormLabel>
                      <FormControl>
                        <Input 
                          type="number"
                          min={0}
                          {...field} 
                          onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dateOfBirth"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Date of Birth</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maritalStatus"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Marital Status</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Identification Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="identificationType"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Identification Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select identification type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="NRIC">NRIC</SelectItem>
                          <SelectItem value="FIN">FIN</SelectItem>
                          <SelectItem value="Passport">Passport</SelectItem>
                          <SelectItem value="Work Permit">Work Permit</SelectItem>
                          <SelectItem value="Employment Pass">Employment Pass</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="identificationNumber"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Identification Number</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="nationality"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Nationality</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="residentialAddress"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Residential Address</FormLabel>
                      <FormControl>
                        <Textarea {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="occupation"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Occupation</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="telephoneNumber"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Telephone Numbers Contact</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emailAddress"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="guardianTelephoneNumber"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Guardian Telephone Number</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="employmentAddress"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Employment Address</FormLabel>
                      <FormControl>
                        <Textarea {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Statement Recorded Section */}
          <Card>
            <CardHeader>
              <CardTitle>Statement Recorded</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="recordingStartTime"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Start Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="recordingEndTime"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>End Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="recordingDate"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="recordingPlace"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Place</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="languageSpoken"
                  render={({ field } : any) => (
                    <FormItem>
                      <FormLabel>Language Spoken</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Interpreter Section */}
              <div className="border-t pt-4">
                <h4 className="font-medium mb-3">Interpreted By (if applicable)</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="interpreterName"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="interpreterContact"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Contact</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {readOnlySignatures ? (
                  <SignatureDisplay
                    signatureUrl={form.watch("interpreterSignature")}
                    label="Interpreter Signature"
                  />
                ) : allowSignaturePreview ? (
                  <SignaturePreview
                    signatureData={form.watch("interpreterSignature")}
                    label="Interpreter Signature"
                    onRetry={() => handleSignatureClick("interpreterSignature")}
                  />
                ) : (
                  <div className="mt-3">
                    <Label>Interpreter Signature</Label>
                    <div className="flex items-center gap-2 mt-1">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleSignatureClick("interpreterSignature")}
                      >
                        <PenTool className="w-4 h-4 mr-2" />
                        {form.watch("interpreterSignature") ? "Update Signature" : "Add Signature"}
                      </Button>
                      {form.watch("interpreterSignature") && (
                        <span className="text-sm text-green-600">✓ Signature captured</span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Recording Officer Section */}
              <div className="border-t pt-4">
                <h4 className="font-medium mb-3">Recorded By</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="recordingOfficerName"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {readOnlySignatures ? (
                    <SignatureDisplay
                      signatureUrl={form.watch("recordingOfficerSignature")}
                      label="Recording Officer Signature"
                    />
                  ) : allowSignaturePreview ? (
                    <SignaturePreview
                      signatureData={form.watch("recordingOfficerSignature")}
                      label="Recording Officer Signature"
                      onRetry={() => handleSignatureClick("recordingOfficerSignature")}
                    />
                  ) : (
                    <div>
                      <Label>Recording Officer Signature</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSignatureClick("recordingOfficerSignature")}
                        >
                          <PenTool className="w-4 h-4 mr-2" />
                          {form.watch("recordingOfficerSignature") ? "Update Signature" : "Add Signature"}
                        </Button>
                        {form.watch("recordingOfficerSignature") && (
                          <span className="text-sm text-green-600">✓ Signature captured</span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Declaration and Signatures Section */}
          <Card>
            <CardHeader>
              <CardTitle>Declaration and Signatures</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600 italic">
                I hereby declare that the details provided in the above are true.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <FormField
                    control={form.control}
                    name="statementMakerName"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Name of person making statement</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {readOnlySignatures ? (
                    <SignatureDisplay
                      signatureUrl={form.watch("statementMakerSignature")}
                      label="Signature of person making statement"
                    />
                  ) : allowSignaturePreview ? (
                    <SignaturePreview
                      signatureData={form.watch("statementMakerSignature")}
                      label="Signature of person making statement"
                      onRetry={() => handleSignatureClick("statementMakerSignature")}
                    />
                  ) : (
                    <div className="mt-3">
                      <Label>Signature of person making statement</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSignatureClick("statementMakerSignature")}
                        >
                          <PenTool className="w-4 h-4 mr-2" />
                          {form.watch("statementMakerSignature") ? "Update Signature" : "Add Signature"}
                        </Button>
                        {form.watch("statementMakerSignature") && (
                          <span className="text-sm text-green-600">✓ Signature captured</span>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <FormField
                    control={form.control}
                    name="scdfInterviewerName"
                    render={({ field } : any) => (
                      <FormItem>
                        <FormLabel>Name of SCDF interviewer</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {readOnlySignatures ? (
                    <SignatureDisplay
                      signatureUrl={form.watch("scdfInterviewerSignature")}
                      label="Signature of SCDF interviewer"
                    />
                  ) : allowSignaturePreview ? (
                    <SignaturePreview
                      signatureData={form.watch("scdfInterviewerSignature")}
                      label="Signature of SCDF interviewer"
                      onRetry={() => handleSignatureClick("scdfInterviewerSignature")}
                    />
                  ) : (
                    <div className="mt-3">
                      <Label>Signature of SCDF interviewer</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleSignatureClick("scdfInterviewerSignature")}
                        >
                          <PenTool className="w-4 h-4 mr-2" />
                          {form.watch("scdfInterviewerSignature") ? "Update Signature" : "Add Signature"}
                        </Button>
                        {form.watch("scdfInterviewerSignature") && (
                          <span className="text-sm text-green-600">✓ Signature captured</span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {showSaveButton && (
            <div className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Save Form
                  </>
                )}
              </Button>
            </div>
          )}
        </form>
      </Form>

      {!readOnlySignatures && (
        <ClientSignatureOverlay
          isOpen={isSignatureOverlayOpen}
          onClose={() => setIsSignatureOverlayOpen(false)}
          onSave={handleSignatureSave}
          existingSignature={(() => {
            if (!currentSignatureField) return undefined
            const signature = form.getValues(currentSignatureField as keyof StatementFormData) as string
            console.log(`Overlay existingSignature for ${currentSignatureField}:`, signature ? signature.substring(0, 50) + '...' : 'empty')
            return signature && signature.trim() !== '' ? signature : undefined
          })()}
          title={`${currentSignatureField} Signature`}
        />
      )}
    </div>
  )
}
