"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { use<PERSON>tom } from "jotai"
import type { TranscriptData, TranscriptSegment, TranscriptSpeaker } from "@/types/database"
import { saveRecording<PERSON>tom, recordingUrlAtom } from "@/store/atoms"

interface WebSocketRecordingConfig {
  websocketUrl: string
  caseId: string
  interviewId: string
  sampleRate?: number
  bufferSize?: number
  onTranscriptionUpdate?: (data: TranscriptData) => void
  onSummaryUpdate?: (summary: string) => void
  onDatabaseSaved?: (recordId: string) => void
  onError?: (error: string) => void
  onConnectionChange?: (connected: boolean) => void
  onFullyProcessed?: () => void
}

interface UseWebSocketRecordingResult {
  isConnected: boolean
  isRecording: boolean
  isPaused: boolean
  transcriptData: TranscriptData | null
  summary: string | null
  databaseRecordId: string | null
  error: string | null
  startRecording: () => Promise<void>
  stopRecording: () => void
  pauseRecording: () => void
  resumeRecording: () => void
  clearTranscript: () => void
}

interface BackendSegment {
  text: string
  speaker: string
  end_time: number
  start_time: number
}

interface StatementInfo {
  full_name: string | null
  aliases: string[] 
  guardian_name: string | null
  guardian_id_number: string | null
  gender: string | null
  age: number | null
  date_of_birth: string | null
  marital_status: string | null
  id_number: string | null
  nationality: string | null
  residential_address: string | null
  occupation: string | null
  employment_address: string | null
  email_address: string | null
  phone_number: string | null
  guardian_phone_number: string | null
  languages_spoken: string[]
}


interface BackendMessage {
  message: string
  segments?: BackendSegment[]
  all_segments?: BackendSegment[]
  summary?: string
  audio_url?: string
  record_id?: string
  supabase_data?: any
  error?: string
  error_type?: string
  statement_info?: StatementInfo | null
}

// Global connection tracking to prevent duplicates
const globalConnections = new Map<string, WebSocket>()

// --- DEBUG Counters (dev only, keep if you want) ---
let audioContextCreated = 0
let processorCreated = 0
let sourceCreated = 0
let sessionCount = 0

const speakerColors = [
  "#2563eb", // Blue
  "#dc3545", // Red
  "#059669", // Green
  "#f59e42", // Orange
  "#a21caf", // Purple
  "#6b7280", // Gray
  "#eab308", // Yellow
  "#3b82f6", // Sky
  "#f43f5e", // Pink
  "#14b8a6", // Teal
  // Add more as needed!
];

export function useWebSocketRecording(config: WebSocketRecordingConfig): UseWebSocketRecordingResult {
  const [isConnected, setIsConnected] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [transcriptData, setTranscriptData] = useState<TranscriptData | null>(null)
  const [summary, setSummary] = useState<string | null>(null)
  const [databaseRecordId, setDatabaseRecordId] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isPaused, setIsPaused] = useState(false)
  const [saveRecording] = useAtom(saveRecordingAtom)
  const [, setRecordingUrl] = useAtom(recordingUrlAtom)

  // Add streaming segment state
  const [streamingSegment, setStreamingSegment] = useState<TranscriptSegment | null>(null)
  const [finalizedSegments, setFinalizedSegments] = useState<TranscriptSegment[]>([])
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now())

  // Refs for managing resources
  const wsRef = useRef<WebSocket | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const processorRef = useRef<ScriptProcessorNode | null>(null)
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null)

  // Control refs
  const hasReceivedFinalSummaryRef = useRef(false)
  const isPausedRef = useRef(isPaused)
  const isUnmountedRef = useRef(false)

  useEffect(() => {
    isPausedRef.current = isPaused
  }, [isPaused])

  const {
    websocketUrl,
    caseId,
    interviewId,
    sampleRate = 16000,
    bufferSize = 4096,
    onTranscriptionUpdate,
    onSummaryUpdate,
    onDatabaseSaved,
    onError,
    onConnectionChange,
    onFullyProcessed,
  } = config

  const connectionKey = `${caseId}_${interviewId}`

  console.log(`🔗 Hook initialized with ID: ${Date.now()}_${Math.random()}`)

  // Speaker management system
  const speakerListRef = useRef<Map<string, TranscriptSpeaker>>(new Map())

  // const generateRandomColor = (): string => {
  //   const colors = [
  //     "#2563eb", "#dc3545", "#16a34a", "#ca8a04", "#9333ea",
  //     "#c2410c", "#0891b2", "#be123c", "#4338ca", "#059669",
  //     "#7c2d12", "#1e40af", "#b91c1c", "#166534", "#a16207"
  //   ]
  //   return colors[Math.floor(Math.random() * colors.length)]
  // }

  const generateRandomColor = (speakerName: string): string => {
    // Simple hashing function to convert a string into a number.
    // This ensures that the same speaker name always produces the same hash.
    const hashCode = (str: string): number => {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char; // A common and effective way to create a hash
            hash |= 0; // Convert to 32bit integer (ensures consistent positive/negative range)
        }
        return Math.abs(hash); // Ensure the hash is positive for modulo operations
    };

    // Helper function to convert a single color component (0-255) to a two-digit hex string
    const toHex = (c: number): string => {
        const hex = Math.round(c).toString(16); // Ensure integer and convert
        return hex.length === 1 ? "0" + hex : hex; // Pad with leading zero if necessary
    };

    // HSV to RGB conversion function
    // h, s, v are expected to be in the range [0, 1]
    const hsvToRgb = (h: number, s: number, v: number): [number, number, number] => {
        let r = 0, g = 0, b = 0;
        const i = Math.floor(h * 6);
        const f = h * 6 - i;
        const p = v * (1 - s);
        const q = v * (1 - f * s);
        const t = v * (1 - (1 - f) * s);

        switch (i % 6) {
            case 0: r = v, g = t, b = p; break;
            case 1: r = q, g = v, b = p; break;
            case 2: r = p, g = v, b = t; break;
            case 3: r = p, g = q, b = v; break;
            case 4: r = t, g = p, b = v; break;
            case 5: r = v, g = p, b = q; break;
        }

        // Convert RGB values from [0, 1] to [0, 255]
        return [r * 255, g * 255, b * 255];
    };

    const hash = hashCode(speakerName);

    // Generate Hue from the hash. Hue is in degrees [0, 360), so normalize to [0, 1) for hsvToRgb.
    // Using a large prime multiplier helps distribute the hue more evenly across the spectrum.
    const hue = (hash * 0.6180339887) % 1; // Golden ratio conjugate for better distribution

    // Set saturation and value (brightness) to fixed, high levels for vibrant colors.
    // Adjust these values if you prefer less saturated or darker/lighter colors.
    const saturation = 0.7; // 70% saturation
    const value = 0.8;      // 80% brightness

    // Convert HSV to RGB
    const [r, g, b] = hsvToRgb(hue, saturation, value);

    // Construct the hexadecimal color string from the RGB components
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  };

  const getSpeakerInfo = (speakerId: string): TranscriptSpeaker => {
    // Check if speaker already exists in our list
    if (speakerListRef.current.has(speakerId)) {
      return speakerListRef.current.get(speakerId)!
    }

    // Create new speaker entry
    const speakerNumber = speakerId.replace('S', '')
    const newSpeaker: TranscriptSpeaker = {
      id: speakerId,
      name: `Speaker ${speakerNumber}`,
      type: "speaker",
      color: generateRandomColor(`Speaker ${speakerNumber}`)
    }

    // Store in our speaker list for future reference
    speakerListRef.current.set(speakerId, newSpeaker)
    return newSpeaker
  }

  const formatTimestamp = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  // New function to aggregate consecutive segments by speaker
  const aggregateSegmentsBySpeaker = useCallback((backendSegments: BackendSegment[]): TranscriptSegment[] => {
    if (!backendSegments || backendSegments.length === 0) return []

    const aggregatedSegments: TranscriptSegment[] = []
    let currentSegment: TranscriptSegment | null = null

    backendSegments.forEach((segment) => {
      const speakerInfo = getSpeakerInfo(segment.speaker)

      if (currentSegment && currentSegment.speaker === speakerInfo.id) {
        // Same speaker - aggregate the text and update end time
        currentSegment.text += " " + segment.text
        // Update timestamp to show range
        const startTime = currentSegment.timestamp.split(" - ")[0] || currentSegment.timestamp
        const endTime = formatTimestamp(segment.end_time)
        currentSegment.timestamp = `${startTime} - ${endTime}`
      } else {
        // New speaker or first segment - save previous and start new
        if (currentSegment) {
          aggregatedSegments.push(currentSegment)
        }

        currentSegment = {
          speaker: speakerInfo.id,
          timestamp: formatTimestamp(segment.start_time),
          text: segment.text,
          confidence: undefined,
        }
      }
    })

    // Don't forget the last segment
    if (currentSegment) {
      aggregatedSegments.push(currentSegment)
    }

    return aggregatedSegments
  }, [])

  // Function to determine which segments are finalized vs streaming
  const processStreamingSegments = useCallback(
    (backendSegments: BackendSegment[]) => {
      const aggregated = aggregateSegmentsBySpeaker(backendSegments)

      if (aggregated.length === 0) {
        return { finalizedSegments: [], streamingSegment: null }
      }

      // Last segment is still streaming, others are finalized
      const finalizedSegments = aggregated.slice(0, -1)
      const streamingSegment = aggregated[aggregated.length - 1]

      return { finalizedSegments, streamingSegment }
    },
    [aggregateSegmentsBySpeaker],
  )

  const cleanupAudio = useCallback(() => {
    try {
      if (processorRef.current) {
        processorRef.current.disconnect()
        processorRef.current = null
      }
      if (sourceRef.current) {
        sourceRef.current.disconnect()
        sourceRef.current = null
      }
      if (audioContextRef.current) {
        audioContextRef.current.close()
        audioContextRef.current = null
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop())
        streamRef.current = null
      }
    } catch (e) {
      // Silent catch: cleanup should be fire-and-forget
    }
  }, [])
//
  const cleanupEverything = useCallback(() => {
    cleanupAudio()
    if (wsRef.current) {
      try {
        wsRef.current.close()
      } catch {}
      wsRef.current = null
    }
    setIsConnected(false)
    setIsRecording(false)
    setIsPaused(false)
  }, [cleanupAudio])

  // Defensive check to avoid duplicate connections
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) return
    if (wsRef.current) {
      try {
        wsRef.current.close()
      } catch {}
      wsRef.current = null
    }

    const wsUrl = `${websocketUrl}?caseID=${encodeURIComponent(caseId)}&interviewID=${encodeURIComponent(interviewId)}`
    wsRef.current = new window.WebSocket(wsUrl)
    wsRef.current.binaryType = "arraybuffer"

    wsRef.current.onopen = () => {
      setIsConnected(true)
      setError(null)
      onConnectionChange?.(true)
    }

    wsRef.current.onmessage = (event) => {
      if (isUnmountedRef.current) return // No state update if unmounted

      try {
        const data: BackendMessage = JSON.parse(event.data)

        switch (data.message) {
          case "PartialTranscript":
            if (data.all_segments) {
              const { finalizedSegments, streamingSegment } = processStreamingSegments(data.all_segments);
              setLastUpdateTime(Date.now());

              // Combine finalized and streaming segments
              const allSegments = streamingSegment ? [...finalizedSegments, streamingSegment] : finalizedSegments;

              // Get all unique speakers from the segments
              const uniqueSpeakerIds = new Set(data.all_segments.map(s => s.speaker))
              const speakers = Array.from(uniqueSpeakerIds).map(id => getSpeakerInfo(id))

              const transcriptData: TranscriptData = {
                speakers,
                segments: allSegments,
                metadata: {
                  segmentCount: allSegments.length,
                  totalDuration: data.all_segments.length > 0 ? Math.max(...data.all_segments.map((s) => s.end_time)) : 0,
                },
              }
              setTranscriptData(transcriptData);
              onTranscriptionUpdate?.(transcriptData);
            }
            break;

          case "AddPartialTranscript":
            // ignore
            break

          case "FinalTranscript":
            if (data.segments) {
              // Final transcript - aggregate all segments
              const aggregatedSegments = aggregateSegmentsBySpeaker(data.segments)

              // Get all unique speakers from the segments
              const uniqueSpeakerIds = new Set(data.segments.map(s => s.speaker))
              const speakers = Array.from(uniqueSpeakerIds).map(id => getSpeakerInfo(id))

              const finalTranscriptData: TranscriptData = {
                speakers,
                segments: aggregatedSegments,
                metadata: {
                  segmentCount: aggregatedSegments.length,
                  totalDuration: data.segments.length > 0 ? Math.max(...data.segments.map((s) => s.end_time)) : 0,
                },
              }
              setTranscriptData(finalTranscriptData);
              onTranscriptionUpdate?.(finalTranscriptData);
            }
            break;

          case "SummaryGenerating":
            // do nothing
            break

          case "FinalSummary":
            if (data.summary) {
              setSummary(data.summary)
              onSummaryUpdate?.(data.summary)
            }

            // -------- Statement Info Console Log ---------
            if (data.statement_info) {
              const info = data.statement_info;
              console.log("Statement Info:");
              Object.entries(info).forEach(([key, value]) => {
                if (Array.isArray(value)) {
                  console.log(`${key}:`, value.join(", "));
                } else {
                  console.log(`${key}:`, value);
                }
              });
            } else {
              console.log("No statement info received from backend.");
            }
            // ---------------------------------------------

            if (data.audio_url) {
              // Print the signed audio URL to your browser console:
              console.log("Signed audio URL from backend:", data.audio_url);

              // Store the recording URL in global state for use during interview creation
              if (saveRecording) {
                setRecordingUrl(data.audio_url);
                console.log("Recording URL stored for interview creation");
              } else {
                console.log("Recording not saved - user opted not to save recordings");
              }
            }
            // Final cleanup!
            hasReceivedFinalSummaryRef.current = true
            if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
              try {
                wsRef.current.close()
              } catch {}
              wsRef.current = null
            }
            cleanupAudio()
            onFullyProcessed?.()
            break

          case "DatabaseSaved":
            if (data.record_id) {
              setDatabaseRecordId(data.record_id)
              onDatabaseSaved?.(data.record_id)
            }
            break

          case "Error":
            {
              const errorMsg = typeof data.error === "string" ? data.error : "Unknown error occurred"
              setError(errorMsg)
              onError?.(errorMsg)
            }
            break

          default:
            break
        }
      } catch (err) {
        setError("Failed to parse backend message")
      }
    }

    wsRef.current.onerror = (e) => {
      setError("WebSocket connection error")
      onError?.("WebSocket connection error")
    }

    wsRef.current.onclose = () => {
      setIsConnected(false)
      setIsRecording(false)
      setIsPaused(false)
      onConnectionChange?.(false)
      cleanupAudio()
    }
  }, [
    websocketUrl,
    caseId,
    interviewId,
    onTranscriptionUpdate,
    onSummaryUpdate,
    onDatabaseSaved,
    onError,
    onConnectionChange,
    onFullyProcessed,
    cleanupAudio,
    processStreamingSegments,
  ])

  // --- Clear speaker list ---
  const clearSpeakerList = useCallback(() => {
    speakerListRef.current.clear()
  }, [])

  // --- Audio init ---
  const initializeAudio = useCallback(async (): Promise<MediaStream> => {
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        sampleRate: sampleRate,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    })
    streamRef.current = stream
    return stream
  }, [sampleRate])

  // --- Start Recording (no duplicate session) ---
  const startRecording = useCallback(async (): Promise<void> => {
    sessionCount += 1
    const sessionId = sessionCount
    if (isRecording) return
    setError(null)

    // Deep clean all nodes and ws just in case
    cleanupEverything()

    // Clear speaker list for new recording session
    clearSpeakerList()

    if (!isConnected || wsRef.current?.readyState !== WebSocket.OPEN) {
      connectWebSocket()
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error("Connection timeout")), 5000)
        const checkConnection = () => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            clearTimeout(timeout)
            resolve(void 0)
          } else {
            setTimeout(checkConnection, 100)
          }
        }
        checkConnection()
      })
    }

    const stream = await initializeAudio()
    const targetSampleRate = 16000

    audioContextCreated += 1
    audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)({
      sampleRate: targetSampleRate,
    })

    sourceCreated += 1
    sourceRef.current = audioContextRef.current.createMediaStreamSource(stream)

    const validBufferSizes = [256, 512, 1024, 2048, 4096, 8192, 16384]
    const idealBufferSize = Math.round(bufferSize * (targetSampleRate / 44100))
    const actualBufferSize = validBufferSizes.find((size) => size >= idealBufferSize) || 4096

    processorCreated += 1
    processorRef.current = audioContextRef.current.createScriptProcessor(actualBufferSize, 1, 1)

    processorRef.current.onaudioprocess = (event) => {
      if (isPausedRef.current || !wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) return

      const inputBuffer = event.inputBuffer.getChannelData(0)
      const actualSampleRate = audioContextRef.current?.sampleRate ?? 44100

      // Simple linear resample to 16k
      function resampleFloat32(input: Float32Array, inputSampleRate: number, outputSampleRate: number): Float32Array {
        if (inputSampleRate === outputSampleRate) return input
        const ratio = inputSampleRate / outputSampleRate
        const outputLength = Math.floor(input.length / ratio)
        const output = new Float32Array(outputLength)
        for (let i = 0; i < outputLength; i++) {
          const pos = i * ratio
          const left = Math.floor(pos)
          const right = Math.min(left + 1, input.length - 1)
          const frac = pos - left
          output[i] = input[left] * (1 - frac) + input[right] * frac
        }
        return output
      }

      const processed = resampleFloat32(inputBuffer, actualSampleRate, targetSampleRate)
      const int16Data = new Int16Array(processed.length)
      for (let i = 0; i < processed.length; i++) {
        const s = Math.max(-1, Math.min(1, processed[i]))
        int16Data[i] = s * 32767
      }

      wsRef.current.send(int16Data.buffer)
    }

    sourceRef.current.connect(processorRef.current)
    processorRef.current.connect(audioContextRef.current.destination)

    setIsRecording(true)
    setIsPaused(false)
    hasReceivedFinalSummaryRef.current = false
  }, [isRecording, isConnected, connectWebSocket, cleanupEverything, initializeAudio, bufferSize, clearSpeakerList])

  // --- Stop Recording (no duplicates) ---
  const stopRecording = useCallback(() => {
    if (!isRecording) return
    setIsRecording(false)
    setIsPaused(false)
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send("END")
    }
    // Audio will be cleaned up after FinalSummary
  }, [isRecording])

  // --- Pause/Resume Recording (no duplicates) ---
  const pauseRecording = useCallback(() => {
    if (!isRecording || isPaused) return
    setIsPaused(true)
    if (audioContextRef.current && audioContextRef.current.state === "running") {
      audioContextRef.current.suspend()
    }
    // Optionally notify backend of pause if needed
  }, [isRecording, isPaused])

  const resumeRecording = useCallback(() => {
    if (!isRecording || !isPaused) return
    setIsPaused(false)
    if (audioContextRef.current && audioContextRef.current.state === "suspended") {
      audioContextRef.current.resume()
    }
  }, [isRecording, isPaused])

  // --- Get current speaker list ---
  const getCurrentSpeakers = useCallback((): TranscriptSpeaker[] => {
    return Array.from(speakerListRef.current.values())
  }, [])

  // --- Transcript + state clear ---
  const clearTranscript = useCallback(() => {
    setTranscriptData(null)
    setSummary(null)
    setDatabaseRecordId(null)
    setStreamingSegment(null)
    setFinalizedSegments([])
    clearSpeakerList() // Clear speaker list when clearing transcript
  }, [clearSpeakerList])

  // --- Cleanup on unmount, always ---
  useEffect(() => {
    isUnmountedRef.current = false
    const cleanup = () => {
      isUnmountedRef.current = true
      cleanupEverything()
    }
    window.addEventListener("beforeunload", cleanup)
    return () => {
      cleanup()
      window.removeEventListener("beforeunload", cleanup)
    }
  }, [cleanupEverything])

  return {
    isConnected,
    isRecording,
    isPaused,
    transcriptData,
    summary,
    databaseRecordId,
    error,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearTranscript,
  }
}
